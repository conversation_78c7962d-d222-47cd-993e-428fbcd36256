[package]
name = "mutant-lib"
version = "0.6.2"
edition = "2021"
license = " LGPL-3.0-only"
description = "Core library for MutAnt distributed mutable key value storage over Autonomi network"
repository = "https://github.com/Champii/MutAnt"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "mutant_lib"
path = "src/lib.rs"

[dependencies]
autonomi = "0.5.3" # never change this for the git.
# ant-networking = "0.3.13"
async-stream = "0.3.5"
blsttc = "8.0.2"
bytes = "1.6.0"
chrono = { version = "0.4.38", features = ["serde"] }
dirs = "5.0"
env_logger = "0.11.3"
futures = "0.3.30"
hex = "0.4.3"
log = "0.4.21"
rand = "0.8.5"
serde = { version = "1.0.203", features = ["derive"] }
serde_cbor = "0.11"
tokio = { version = "1.37.0", features = ["full"] }
tokio-util = { version = "0.7.11", features = ["codec"] }
tokio-stream = "0.1"
thiserror = "1.0"
tokio-retry = "0.3"
async-trait = "0.1"
once_cell = "1.19"
sha2 = "0.10"
deadpool = { version = "0.12.1", features = ["rt_tokio_1", "managed"] }
k256 = { version = "0.13", features = ["ecdsa"] }
hkdf = "0.12"
ring = "0.17"
serde_json = "1.0"
anyhow = "1.0"
bincode = "1.3"
xdg = "2.5.2"
mutant-protocol = { path = "../mutant-protocol", version = "0.6.2" }
async-channel = "2.3.1"
never = "0.1.0"
crc = "3.2.1"
lazy_static = "1.5.0"

[dev-dependencies]
# assert_matches = "1.5" # Removed
dialoguer = "0.11.0"
indicatif = { version = "0.17.7", features = ["tokio"] }
clap = { version = "4.4.8", features = ["derive"] }
pretty_env_logger = "0.5.0"
serial_test = "3.0.0"
tempfile = "3.10.1"

[features]
default = []
