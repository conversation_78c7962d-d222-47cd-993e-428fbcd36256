[package]
name = "mutant-daemon"
version = "0.6.2"
edition = "2021"
license = "LGPL-3.0-only"
description = "Background daemon service for MutAnt distributed mutable key value storage over Autonomi network"
repository = "https://github.com/Champii/MutAnt"

[dependencies]
anyhow = "1.0"
autonomi = "0.5.3"
chrono = { version = "0.4", features = ["serde"] }
clap = { version = "4.4", features = ["derive"] }
dotenvy = "0.15"
#colonylib = { path = "../../colonylib" }
colonylib = { git = "https://github.com/zettawatt/colonylib", branch = "main", commit = "b0d0ef8767cb0061d8965a4e9c0621b2986e0bf4" }
dialoguer = "0.11"
directories = "5.0"
env_logger = "0.11"
log = "0.4"
mutant-lib = { path = "../mutant-lib", version = "0.6.2" }
mutant-protocol = { path = "../mutant-protocol", version = "0.6.2" }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
thiserror = "1.0"
tokio = { version = "1.0", features = ["full"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
warp = "0.3"
xdg = "2.5"

futures-util = "0.3"
lazy_static = "1.4"
lockfile = "0.4.0"
ffmpeg-next = "7.1"
hex = "0.4.3"
sha2 = "0.10.9"
hkdf = "0.12.4"
urlencoding = "2.1.3"
